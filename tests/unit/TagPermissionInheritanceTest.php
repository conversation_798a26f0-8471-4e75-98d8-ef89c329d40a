<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Tests\Unit;

use Fargot132\TagPermissions\Access\TagPermissionInheritancePolicy;
use PHPUnit\Framework\TestCase;
use Mockery as m;

class TagPermissionInheritanceTest extends TestCase
{
    protected function tearDown(): void
    {
        m::close();
    }

    public function testAdminUserHasAllPermissions()
    {
        $policy = new TagPermissionInheritancePolicy();

        $user = m::mock('Flarum\User\User');
        $user->shouldReceive('isAdmin')->andReturn(true);

        $tag = m::mock('Flarum\Tags\Tag');

        $result = $policy->can($user, 'startDiscussion', $tag);

        $this->assertEquals('ALLOW', $result);
    }

    public function testPolicyClassExists()
    {
        $policy = new TagPermissionInheritancePolicy();
        $this->assertInstanceOf(TagPermissionInheritancePolicy::class, $policy);
    }
}
