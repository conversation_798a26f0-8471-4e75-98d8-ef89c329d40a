{"name": "fargot132/flarum-tag-permissions", "description": "Enables tag permissions inhetitance", "keywords": ["flarum"], "type": "flarum-extension", "license": "MIT", "require": {"flarum/core": "^1.2.0", "php": "^8.3", "flarum/tags": "^1.8"}, "authors": [{"name": "<PERSON><PERSON>", "role": "Developer"}], "autoload": {"psr-4": {"Fargot132\\TagPermissions\\": "src/"}}, "extra": {"flarum-extension": {"title": "Tag Permissions", "category": "", "icon": {"name": "", "color": "", "backgroundColor": ""}}, "flarum-cli": {"modules": {"admin": true, "forum": true, "js": true, "jsCommon": true, "css": true, "locale": true, "gitConf": true, "githubActions": false, "prettier": true, "typescript": true, "bundlewatch": false, "backendTesting": true, "editorConfig": true, "styleci": true}}}, "minimum-stability": "dev", "prefer-stable": true, "autoload-dev": {"psr-4": {"Fargot132\\TagPermissions\\Tests\\": "tests/"}}, "scripts": {"test": ["@test:unit", "@test:integration"], "test:unit": "phpunit -c tests/phpunit.unit.xml", "test:integration": "phpunit -c tests/phpunit.integration.xml", "test:setup": "@php tests/integration/setup.php"}, "scripts-descriptions": {"test": "Runs all tests.", "test:unit": "Runs all unit tests.", "test:integration": "Runs all integration tests.", "test:setup": "Sets up a database for use with integration tests. Execute this only once."}, "require-dev": {"flarum/testing": "^1.0.0"}}