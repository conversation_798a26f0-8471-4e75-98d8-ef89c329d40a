<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Listener;

use Flarum\Group\Permission;
use Flarum\Tags\Event\Saving as TagSaving;
use Flarum\Tags\Tag;

class PropagateTagPermissions
{
    public function handle(TagSaving $event)
    {
        $tag = $event->tag;
        
        // Only propagate if this is a parent tag and permissions have changed
        if ($this->shouldPropagatePermissions($tag)) {
            $this->propagateToChildren($tag);
        }
    }

    protected function shouldPropagatePermissions(Tag $tag): bool
    {
        // Check if tag has children and is restricted
        return $tag->children()->exists() && 
               $tag->is_restricted && 
               ($tag->wasChanged('is_restricted') || $tag->isDirty());
    }

    protected function propagateToChildren(Tag $parentTag): void
    {
        $children = $parentTag->children()->get();
        
        foreach ($children as $child) {
            // Get all permissions for the parent tag
            $parentPermissions = Permission::where('permission', 'like', "tag{$parentTag->id}.%")->get();
            
            foreach ($parentPermissions as $permission) {
                // Extract the ability from the parent permission
                $ability = substr($permission->permission, strlen("tag{$parentTag->id}."));
                
                // Create or update the child permission
                $childPermissionName = "tag{$child->id}.$ability";
                
                Permission::updateOrCreate(
                    [
                        'group_id' => $permission->group_id,
                        'permission' => $childPermissionName,
                    ],
                    [
                        'group_id' => $permission->group_id,
                        'permission' => $childPermissionName,
                    ]
                );
            }
            
            // Recursively propagate to grandchildren
            if ($child->children()->exists()) {
                $this->propagateToChildren($child);
            }
        }
    }
}
