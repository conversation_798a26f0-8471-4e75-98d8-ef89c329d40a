<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

namespace Fargot132\TagPermissions\Api\Controller;

use Flarum\Api\Controller\AbstractShowController;
use Flarum\Http\RequestUtil;
use Flarum\Tags\Api\Serializer\TagSerializer;
use Flarum\Tags\Tag;
use Illuminate\Support\Arr;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;

class UpdateTagInheritanceController extends AbstractShowController
{
    public $serializer = TagSerializer::class;

    public function data(ServerRequestInterface $request, Document $document)
    {
        $actor = RequestUtil::getActor($request);
        $actor->assertAdmin();

        $id = Arr::get($request->getQueryParams(), 'id');
        $tag = Tag::findOrFail($id);

        $attributes = Arr::get($request->getParsedBody(), 'data.attributes', []);
        
        // Handle inheritance settings
        if (isset($attributes['inheritPermissions'])) {
            $inheritPermissions = (bool) $attributes['inheritPermissions'];
            
            // Store inheritance setting in tag's meta or create a separate table
            // For now, we'll use a simple approach with tag description or custom field
            $tag->setAttribute('inherit_permissions', $inheritPermissions);
        }

        $tag->save();

        return $tag;
    }
}
