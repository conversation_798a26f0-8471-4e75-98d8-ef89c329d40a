<?php

/*
 * This file is part of fargot132/flarum-tag-permissions.
 *
 * Copyright (c) 2025 <PERSON><PERSON>.
 *
 * For the full copyright and license information, please view the LICENSE.md
 * file that was distributed with this source code.
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Schema\Builder;

return [
    'up' => function (Builder $schema) {
        $schema->table('tags', function (Blueprint $table) {
            $table->boolean('inherit_permissions')->default(false)->after('is_hidden');
        });
    },
    
    'down' => function (Builder $schema) {
        $schema->table('tags', function (Blueprint $table) {
            $table->dropColumn('inherit_permissions');
        });
    }
];
