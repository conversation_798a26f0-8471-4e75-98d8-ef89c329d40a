// Tag collapse/expand feature styles
.collapse-indicator {
  font-size: 12px;
  opacity: 0.7;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    opacity: 1;
    transform: scale(1.2);
  }
}

.TagTile {
  transition: opacity 0.3s ease, transform 0.3s ease;

  &[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
  }
}

// Visual hierarchy for child tags
.TagTile {
  position: relative;

  // Add indentation for child tags (this is a visual enhancement)
  &:not(:first-child) {
    // Check if this might be a child tag by looking at common patterns
    &[class*="child"],
    &[data-parent],
    &.tag-child {
      margin-left: 20px;

      &::before {
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        width: 10px;
        height: 1px;
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}