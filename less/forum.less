// Tag collapse/expand feature styles
.collapse-toggle {
  background: none;
  border: none;
  color: inherit;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  margin: 4px 0;
  border-radius: 3px;
  transition: all 0.2s ease;
  opacity: 0.7;
  display: block;
  width: auto;

  &:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  &:focus {
    outline: 1px solid rgba(255, 255, 255, 0.3);
    outline-offset: 2px;
  }
}

.TagTile-children {
  transition: all 0.3s ease;
  overflow: hidden;

  &[style*="display: none"] {
    opacity: 0;
    max-height: 0;
    margin: 0;
    padding: 0;
  }
}

// Ensure TagTile has relative positioning for proper layout
.TagTile {
  position: relative;
}