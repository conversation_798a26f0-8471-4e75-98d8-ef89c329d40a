// Sidebar tag collapse/expand feature styles
.sideNav .collapse-toggle {
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  opacity: 0.7;
  user-select: none;
  margin-left: 5px;

  &:hover {
    opacity: 1;
    transform: scale(1.2);
  }
}

// Smooth transitions for collapsing sidebar navigation
.sideNav ul {
  transition: all 0.3s ease;
  overflow: hidden;

  &[style*="display: none"] {
    opacity: 0;
    max-height: 0;
    margin: 0;
    padding: 0;
  }
}

// Ensure proper spacing for nested navigation
.sideNav li {
  position: relative;

  ul {
    margin-left: 15px;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding-left: 10px;
  }
}