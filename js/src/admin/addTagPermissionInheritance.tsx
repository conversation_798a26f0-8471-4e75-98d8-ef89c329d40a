import app from 'flarum/admin/app';
import { extend } from 'flarum/common/extend';
import Switch from 'flarum/common/components/Switch';
import EditTagModal from 'flarum/tags/components/EditTagModal';

export default function addTagPermissionInheritance() {
  extend(EditTagModal.prototype, 'fields', function (items) {
    const tag = this.tag;

    // Only show inheritance option for parent tags (tags that have children)
    if (tag && tag.children && tag.children().length > 0) {
      items.add(
        'inheritPermissions',
        <div className="Form-group">
          <label>{app.translator.trans('fargot132-tag-permissions.admin.inherit_permissions_label')}</label>
          <div>
            <Switch
              state={this.tag.inheritPermissions() || false}
              onchange={(value: boolean) => {
                this.tag.pushAttributes({ inheritPermissions: value });
              }}
            >
              {app.translator.trans('fargot132-tag-permissions.admin.inherit_permissions_help')}
            </Switch>
          </div>
        </div>,
        10
      );
    }
  });

  extend(EditTagModal.prototype, 'submitData', function (data) {
    if (this.tag.inheritPermissions !== undefined) {
      data.inheritPermissions = this.tag.inheritPermissions();
    }
  });
}
