import app from 'flarum/forum/app';
import { extend } from 'flarum/common/extend';

export default function addTagCollapseFeature() {
  // Store collapsed state in session storage
  const collapsedTags = new Set(JSON.parse(sessionStorage.getItem('collapsedTags') || '[]'));

  const saveCollapsedState = () => {
    sessionStorage.setItem('collapsedTags', JSON.stringify([...collapsedTags]));
  };

  const isTagCollapsed = (tagSlug: string) => {
    return collapsedTags.has(tagSlug);
  };

  const toggleTagCollapse = (tagSlug: string) => {
    if (collapsedTags.has(tagSlug)) {
      collapsedTags.delete(tagSlug);
    } else {
      collapsedTags.add(tagSlug);
    }
    saveCollapsedState();
    // Force a redraw
    m.redraw();
  };

  // Simple approach: add click handlers to tag links
  const addClickHandlers = () => {
    // Wait for tags to be loaded
    setTimeout(() => {
      const tagLinks = document.querySelectorAll('.TagTile a, .TagLabel');

      tagLinks.forEach((link: Element) => {
        const linkElement = link as HTMLElement;
        const href = linkElement.getAttribute('href');

        if (href && href.includes('/t/')) {
          // Extract tag slug from URL
          const tagSlug = href.split('/t/')[1];

          // Check if this tag has children by looking at the tag store
          const tag = app.store.getBy('tags', 'slug' as any, tagSlug);

          if (tag && (tag as any).children && (tag as any).children().length > 0) {
            // Add collapse indicator
            if (!linkElement.querySelector('.collapse-indicator')) {
              const indicator = document.createElement('span');
              indicator.className = 'collapse-indicator';
              indicator.innerHTML = isTagCollapsed(tagSlug) ? ' ▶' : ' ▼';
              indicator.style.cursor = 'pointer';
              indicator.style.marginLeft = '5px';

              indicator.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                toggleTagCollapse(tagSlug);
                indicator.innerHTML = isTagCollapsed(tagSlug) ? ' ▶' : ' ▼';
                hideChildTags(tagSlug);
              });

              linkElement.appendChild(indicator);
            }
          }
        }
      });

      // Hide collapsed children on initial load
      collapsedTags.forEach((tagSlug) => hideChildTags(tagSlug as string));
    }, 1000);
  };

  const hideChildTags = (parentSlug: string) => {
    // Find the parent tag
    const parentTag = app.store.getBy('tags', 'slug' as any, parentSlug);
    if (!parentTag || !(parentTag as any).children) return;

    // Hide/show child tags
    (parentTag as any).children().forEach((childTag: any) => {
      const childSlug = childTag.slug();
      const childElements = document.querySelectorAll(`a[href*="/t/${childSlug}"]`);

      childElements.forEach((element: Element) => {
        const tagTile = element.closest('.TagTile');
        if (tagTile) {
          const tileElement = tagTile as HTMLElement;
          if (isTagCollapsed(parentSlug)) {
            tileElement.style.display = 'none';
          } else {
            tileElement.style.display = '';
          }
        }
      });

      // Recursively hide grandchildren if this child is also collapsed
      if (isTagCollapsed(childSlug)) {
        hideChildTags(childSlug);
      }
    });
  };

  // Initialize the feature
  const init = () => {
    // Add click handlers when the page loads
    addClickHandlers();

    // Re-add handlers when navigating (SPA behavior)
    const originalPushState = history.pushState;
    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      setTimeout(addClickHandlers, 500);
    };

    // Also listen for popstate (back/forward navigation)
    window.addEventListener('popstate', () => {
      setTimeout(addClickHandlers, 500);
    });
  };

  init();
}
