<!DOCTYPE html>
<html>
<head>
    <title>Tag Collapse Test</title>
    <style>
        .TagTile {
            border: 1px solid #ccc;
            margin: 10px;
            padding: 10px;
            background: #f5f5f5;
        }
        
        .TagTile-children {
            margin-top: 10px;
            padding: 5px;
            background: #e0e0e0;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .TagTile-children[style*="display: none"] {
            opacity: 0;
            max-height: 0;
            margin: 0;
            padding: 0;
        }
        
        .collapse-toggle {
            background: none;
            border: none;
            color: inherit;
            font-size: 14px;
            cursor: pointer;
            padding: 4px 8px;
            margin: 4px 0;
            border-radius: 3px;
            transition: all 0.2s ease;
            opacity: 0.7;
            display: block;
            width: auto;
        }
        
        .collapse-toggle:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.1);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <h1>Tag Collapse Feature Test</h1>
    
    <div class="TagTile">
        <a class="TagTile-info" href="/t/parent-tag">
            <h3>Parent Tag</h3>
            <p>This is a parent tag with children</p>
        </a>
        <div class="TagTile-children">
            <a href="/t/child-tag-1">Child Tag 1</a>
            <a href="/t/child-tag-2">Child Tag 2</a>
            <a href="/t/child-tag-3">Child Tag 3</a>
        </div>
    </div>
    
    <div class="TagTile">
        <a class="TagTile-info" href="/t/another-parent">
            <h3>Another Parent Tag</h3>
            <p>Another parent tag with children</p>
        </a>
        <div class="TagTile-children">
            <a href="/t/child-a">Child A</a>
            <a href="/t/child-b">Child B</a>
        </div>
    </div>
    
    <script>
        // Simulate the collapse functionality
        const collapsedTags = new Set();
        
        function isTagCollapsed(tagSlug) {
            return collapsedTags.has(tagSlug);
        }
        
        function toggleTagCollapse(tagSlug) {
            if (collapsedTags.has(tagSlug)) {
                collapsedTags.delete(tagSlug);
            } else {
                collapsedTags.add(tagSlug);
            }
        }
        
        function addCollapseToTagTiles() {
            const tagTiles = document.querySelectorAll('.TagTile');
            
            tagTiles.forEach((tile) => {
                const childrenContainer = tile.querySelector('.TagTile-children');
                
                if (childrenContainer && !tile.querySelector('.collapse-toggle')) {
                    const mainLink = tile.querySelector('.TagTile-info');
                    if (!mainLink) return;
                    
                    const href = mainLink.getAttribute('href');
                    if (!href) return;
                    
                    const tagSlug = href.split('/t/')[1];
                    if (!tagSlug) return;
                    
                    const toggle = document.createElement('button');
                    toggle.className = 'collapse-toggle';
                    toggle.innerHTML = isTagCollapsed(tagSlug) ? '▶' : '▼';
                    toggle.title = isTagCollapsed(tagSlug) ? 'Expand children' : 'Collapse children';
                    
                    toggle.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleTagCollapse(tagSlug);
                        
                        toggle.innerHTML = isTagCollapsed(tagSlug) ? '▶' : '▼';
                        toggle.title = isTagCollapsed(tagSlug) ? 'Expand children' : 'Collapse children';
                        
                        if (isTagCollapsed(tagSlug)) {
                            childrenContainer.style.display = 'none';
                        } else {
                            childrenContainer.style.display = '';
                        }
                    });
                    
                    childrenContainer.parentNode.insertBefore(toggle, childrenContainer);
                    
                    if (isTagCollapsed(tagSlug)) {
                        childrenContainer.style.display = 'none';
                    }
                }
            });
        }
        
        // Initialize
        addCollapseToTagTiles();
    </script>
</body>
</html>
